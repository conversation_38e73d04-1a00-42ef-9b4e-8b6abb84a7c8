# ============================================================================
# QA LIBRARY WITH TYPESENSE INTEGRATION
# ============================================================================

import os
import json
import hashlib
import re
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
import requests
from dotenv import load_dotenv
import logging

# Import from the existing ai.py module
from ai import (
    get_env_var, 
    LLMRouter, 
    create_router_from_env,
    count_tokens_enhanced,
    clean_json_response
)

# Import for embeddings (with error handling)
try:
    from langchain_openai import OpenAIEmbeddings
    from langchain_experimental.text_splitter import SemanticChunker
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    print("⚠️  LangChain not available - using fallback text processing")

# Import for document processing
try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️  python-docx not available - DOCX processing disabled")

from pathlib import Path

# Load environment variables
load_dotenv(".env")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# CONSTANTS AND CONFIGURATION
# ============================================================================

# Typesense Configuration with defaults
TYPESENSE_HOST = get_env_var('TYPESENSE_HOST', 'localhost')
TYPESENSE_PORT = get_env_var('TYPESENSE_PORT', '8108')
TYPESENSE_API_KEY = get_env_var('TYPESENSE_API_KEY', 'xyz')
TYPESENSE_PROTOCOL = get_env_var('TYPESENSE_PROTOCOL', 'http')
DOCUMENTS_COLLECTION = get_env_var('TYPESENSE_DOCUMENTS_COLLECTION', 'documents')

# Processing Configuration
DEFAULT_CHUNK_SIZE = 1000
DEFAULT_CHUNK_OVERLAP = 200
DEFAULT_SEARCH_RESULTS = 5
DEFAULT_CONTEXT_DOCS = 3

# QA prompt templates (optimized for better responses)
QA_PROMPT_TEMPLATE = """Dựa trên các tài liệu được cung cấp, hãy trả lời câu hỏi một cách chính xác và chi tiết.

TÀI LIỆU THAM KHẢO:
{context}

CÂUHỎI: {question}

HƯỚNG DẪN TRẢ LỜI:
- Sử dụng thông tin từ tài liệu để trả lời
- Trả lời bằng tiếng Việt rõ ràng, dễ hiểu
- Nếu không tìm thấy thông tin liên quan, nói rõ "Không có thông tin trong tài liệu"
- Trích dẫn nguồn khi cần thiết

TRẢ LỜI:"""

CONTEXTUAL_QA_PROMPT_TEMPLATE = """Bạn là một trợ lý AI chuyên nghiệp. Hãy trả lời câu hỏi dựa trên tài liệu và ngữ cảnh cuộc hội thoại.

TÀI LIỆU THAM KHẢO:
{context}

LỊCH SỬ HỘI THOẠI:
{history}

CÂUHỎI HIỆN TẠI: {question}

HƯỚNG DẪN:
- Tham khảo lịch sử để hiểu ngữ cảnh
- Trả lời dựa trên thông tin trong tài liệu
- Sử dụng tiếng Việt tự nhiên và chuyên nghiệp
- Nếu không có thông tin, hãy nói rõ

TRẢ LỜI:"""

# ============================================================================
# DATA CLASSES
# ============================================================================

@dataclass
class Document:
    """Represents a document with metadata"""
    id: str
    title: str
    content: str
    file_path: str
    file_type: str
    created_at: datetime
    updated_at: datetime
    chunk_index: int = 0
    total_chunks: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary for storage"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'created_at': int(self.created_at.timestamp()),
            'updated_at': int(self.updated_at.timestamp()),
            'chunk_index': self.chunk_index,
            'total_chunks': self.total_chunks,
            'metadata': json.dumps(self.metadata)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """Create document from dictionary"""
        return cls(
            id=data['id'],
            title=data['title'],
            content=data['content'],
            file_path=data['file_path'],
            file_type=data['file_type'],
            created_at=datetime.fromtimestamp(data['created_at']),
            updated_at=datetime.fromtimestamp(data['updated_at']),
            chunk_index=data.get('chunk_index', 0),
            total_chunks=data.get('total_chunks', 1),
            metadata=json.loads(data.get('metadata', '{}'))
        )

@dataclass
class SearchResult:
    """Represents a search result from Typesense"""
    document: Document
    score: float
    highlights: List[str] = field(default_factory=list)
    
    def get_summary(self) -> str:
        """Get a summary of the search result"""
        summary = f"Document: {self.document.title} (Score: {self.score:.2f})"
        if self.highlights:
            summary += f"\nHighlights: {len(self.highlights)} matches"
        return summary

@dataclass
class QAResponse:
    """Represents a Q&A response"""
    question: str
    answer: str
    sources: List[SearchResult]
    model_used: str
    tokens_used: int
    confidence: float
    timestamp: datetime
    
    def get_summary(self) -> str:
        """Get a summary of the QA response"""
        return f"Q: {self.question}\nA: {self.answer}\nSources: {len(self.sources)} documents\nConfidence: {self.confidence:.2f}"

# ============================================================================
# TYPESENSE CLIENT
# ============================================================================

class TypesenseClient:
    """Typesense client for document indexing and search"""
    
    def __init__(self, host: str = None, port: str = None, api_key: str = None, protocol: str = None):
        self.host = host or TYPESENSE_HOST
        self.port = port or TYPESENSE_PORT
        self.api_key = api_key or TYPESENSE_API_KEY
        self.protocol = protocol or TYPESENSE_PROTOCOL
        self.base_url = f"{self.protocol}://{self.host}:{self.port}"
        
        # Headers for requests
        self.headers = {
            'X-TYPESENSE-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Typesense"""
        url = f"{self.base_url}/{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=data)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=self.headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Typesense request failed: {e}")
            raise
    
    def create_collection(self, collection_name: str) -> bool:
        """Create a collection for documents"""
        schema = {
            "name": collection_name,
            "fields": [
                {"name": "id", "type": "string"},
                {"name": "title", "type": "string"},
                {"name": "content", "type": "string"},
                {"name": "file_path", "type": "string"},
                {"name": "file_type", "type": "string"},
                {"name": "created_at", "type": "int32"},
                {"name": "updated_at", "type": "int32"},
                {"name": "chunk_index", "type": "int32"},
                {"name": "total_chunks", "type": "int32"},
                {"name": "metadata", "type": "string"}
            ]
        }
        
        try:
            self._make_request('POST', 'collections', schema)
            print(f"✅ Collection '{collection_name}' created successfully")
            return True
        except Exception as e:
            if "already exists" in str(e).lower():
                print(f"ℹ️  Collection '{collection_name}' already exists")
                return True
            print(f"❌ Failed to create collection: {e}")
            return False
    
    def index_document(self, collection_name: str, document: Document) -> bool:
        """Index a document"""
        try:
            self._make_request('POST', f'collections/{collection_name}/documents', document.to_dict())
            return True
        except Exception as e:
            print(f"❌ Failed to index document {document.id}: {e}")
            return False
    
    def search_documents(self, collection_name: str, query: str, 
                        num_results: int = 10, search_fields: List[str] = None) -> List[SearchResult]:
        """Search documents in the collection"""
        if search_fields is None:
            search_fields = ['title', 'content']
        
        search_params = {
            'q': query,
            'query_by': ','.join(search_fields),
            'per_page': num_results,
            'sort_by': '_text_match:desc',
            'highlight_fields': 'title,content',
            'highlight_affix_num_tokens': 3
        }
        
        try:
            response = self._make_request('GET', f'collections/{collection_name}/documents/search', search_params)
            
            results = []
            for hit in response.get('hits', []):
                doc_data = hit['document']
                
                # Convert back to Document object
                doc = Document(
                    id=doc_data['id'],
                    title=doc_data['title'],
                    content=doc_data['content'],
                    file_path=doc_data['file_path'],
                    file_type=doc_data['file_type'],
                    created_at=datetime.fromtimestamp(doc_data['created_at']),
                    updated_at=datetime.fromtimestamp(doc_data['updated_at']),
                    chunk_index=doc_data['chunk_index'],
                    total_chunks=doc_data['total_chunks'],
                    metadata=json.loads(doc_data.get('metadata', '{}'))
                )
                
                # Extract highlights
                highlights = []
                hit_highlights = hit.get('highlights', {})
                
                # Handle both dictionary and list formats for highlights
                if isinstance(hit_highlights, dict):
                    for field, highlight_list in hit_highlights.items():
                        if isinstance(highlight_list, list):
                            for highlight in highlight_list:
                                if isinstance(highlight, dict):
                                    highlights.append(highlight.get('snippet', ''))
                                else:
                                    highlights.append(str(highlight))
                        else:
                            highlights.append(str(highlight_list))
                elif isinstance(hit_highlights, list):
                    for highlight in hit_highlights:
                        if isinstance(highlight, dict):
                            highlights.append(highlight.get('snippet', ''))
                        else:
                            highlights.append(str(highlight))
                
                results.append(SearchResult(
                    document=doc,
                    score=hit['text_match_info']['score'],
                    highlights=highlights
                ))
            
            return results
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
    
    def delete_document(self, collection_name: str, document_id: str) -> bool:
        """Delete a document from the collection"""
        try:
            self._make_request('DELETE', f'collections/{collection_name}/documents/{document_id}')
            return True
        except Exception as e:
            print(f"❌ Failed to delete document {document_id}: {e}")
            return False
    
    def get_collection_stats(self, collection_name: str) -> Dict:
        """Get collection statistics"""
        try:
            return self._make_request('GET', f'collections/{collection_name}')
        except Exception as e:
            print(f"❌ Failed to get collection stats: {e}")
            return {}

# ============================================================================
# MOCK TYPESENSE CLIENT FOR TESTING
# ============================================================================

class MockTypesenseClient:
    """Mock Typesense client for testing when Typesense server is not available"""
    
    def __init__(self):
        self.collections = {}
        self.documents = {}
        logger.info("Using Mock Typesense Client - Search functionality will be limited")
    
    def create_collection(self, collection_name: str) -> bool:
        """Create a mock collection"""
        self.collections[collection_name] = {
            'name': collection_name,
            'num_documents': 0,
            'created_at': datetime.now().isoformat()
        }
        logger.info(f"Mock collection '{collection_name}' created")
        return True
    
    def index_document(self, collection_name: str, document: Document) -> bool:
        """Index a document in mock storage"""
        if collection_name not in self.collections:
            self.create_collection(collection_name)
        
        if collection_name not in self.documents:
            self.documents[collection_name] = []
        
        self.documents[collection_name].append(document)
        self.collections[collection_name]['num_documents'] += 1
        return True
    
    def search_documents(self, collection_name: str, query: str, 
                        num_results: int = 10, search_fields: List[str] = None) -> List[SearchResult]:
        """Perform enhanced text search in mock storage"""
        if collection_name not in self.documents:
            return []
        
        results = []
        query_lower = query.lower()
        
        # Enhanced keyword extraction
        query_keywords = self._extract_keywords(query)
        
        for doc in self.documents[collection_name]:
            score, highlights = self._calculate_relevance(doc, query_lower, query_keywords)
            
            if score > 0:
                results.append(SearchResult(
                    document=doc,
                    score=score,
                    highlights=highlights
                ))
        
        # Sort by score and return top results
        results.sort(key=lambda x: x.score, reverse=True)
        return results[:num_results]
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract meaningful keywords from query"""
        # Remove common Vietnamese stop words
        stop_words = {'là', 'của', 'trong', 'với', 'này', 'đó', 'có', 'không', 'được', 'cho', 'về', 'từ', 'theo', 'như', 'khi', 'nào', 'gì', 'ai', 'đâu', 'sao', 'thế', 'nào'}
        
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return keywords
    
    def _calculate_relevance(self, doc: Document, query_lower: str, query_keywords: List[str]) -> tuple:
        """Calculate relevance score and highlights for a document"""
        score = 0
        highlights = []
        
        title_lower = doc.title.lower()
        content_lower = doc.content.lower()
        
        # Exact query match bonus
        if query_lower in content_lower:
            score += 100
            highlights.append(f"Exact match: {self._extract_sentence(doc.content, query_lower)}")
        
        # Title matching
        title_matches = sum(1 for keyword in query_keywords if keyword in title_lower)
        if title_matches > 0:
            score += title_matches * 30
            highlights.append(f"Title: {doc.title}")
        
        # Content keyword matching
        content_matches = sum(1 for keyword in query_keywords if keyword in content_lower)
        if content_matches > 0:
            score += content_matches * 15
            
            # Find relevant sentences
            sentences = self._split_sentences(doc.content)
            for sentence in sentences:
                if any(keyword in sentence.lower() for keyword in query_keywords):
                    highlights.append(f"Content: {sentence.strip()}")
                    if len(highlights) >= 3:  # Limit highlights
                        break
        
        # Vietnamese semantic matching
        semantic_score = self._vietnamese_semantic_match(content_lower, query_lower)
        score += semantic_score
        
        return score, highlights[:3]  # Return top 3 highlights
    
    def _extract_sentence(self, text: str, query: str) -> str:
        """Extract sentence containing the query"""
        sentences = self._split_sentences(text)
        for sentence in sentences:
            if query in sentence.lower():
                return sentence.strip()
        return ""
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _vietnamese_semantic_match(self, content: str, query: str) -> int:
        """Vietnamese semantic matching for common concepts"""
        semantic_patterns = {
            'mục đích': ['mục đích', 'mục tiêu', 'nhằm', 'để', 'goal', 'purpose'],
            'chuẩn bị': ['chuẩn bị', 'sẵn sàng', 'trang bị', 'preparation', 'prepare'],
            'tiến hành': ['tiến hành', 'thực hiện', 'tiến hành', 'bắt đầu', 'execute', 'conduct'],
            'ngày tháng': ['ngày', 'tháng', 'thời gian', 'date', 'time', 'khi'],
            'hoạt động': ['hoạt động', 'sự kiện', 'chương trình', 'activity', 'event']
        }
        
        score = 0
        for concept, patterns in semantic_patterns.items():
            if any(pattern in query for pattern in patterns):
                for pattern in patterns:
                    if pattern in content:
                        score += 10
                        break
        
        return score
    
    def delete_document(self, collection_name: str, document_id: str) -> bool:
        """Delete a document from mock storage"""
        if collection_name not in self.documents:
            return False
        
        original_count = len(self.documents[collection_name])
        self.documents[collection_name] = [
            doc for doc in self.documents[collection_name] 
            if doc.id != document_id
        ]
        
        deleted = len(self.documents[collection_name]) < original_count
        if deleted:
            self.collections[collection_name]['num_documents'] -= 1
        
        return deleted
    
    def get_collection_stats(self, collection_name: str) -> Dict:
        """Get mock collection statistics"""
        if collection_name not in self.collections:
            return {}
        
        stats = self.collections[collection_name].copy()
        stats['num_documents'] = len(self.documents.get(collection_name, []))
        return stats

# ============================================================================
# ENHANCED TYPESENSE CLIENT WITH FALLBACK
# ============================================================================

class EnhancedTypesenseClient:
    """Enhanced Typesense client with fallback to mock when server is unavailable"""
    
    def __init__(self, host: str = None, port: str = None, api_key: str = None, protocol: str = None):
        self.host = host or TYPESENSE_HOST
        self.port = port or TYPESENSE_PORT
        self.api_key = api_key or TYPESENSE_API_KEY
        self.protocol = protocol or TYPESENSE_PROTOCOL
        self.base_url = f"{self.protocol}://{self.host}:{self.port}"
        
        # Headers for requests
        self.headers = {
            'X-TYPESENSE-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }
        
        # Try to connect to Typesense
        self.use_mock = False
        self.real_client = None
        self.mock_client = None
        
        try:
            # Test connection
            import requests
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.real_client = TypesenseClient(host, port, api_key, protocol)
                print("✅ Connected to Typesense server")
            else:
                raise Exception(f"Server returned status {response.status_code}")
        except Exception as e:
            print(f"⚠️  Typesense server not available: {e}")
            print("🔄 Falling back to mock client")
            self.use_mock = True
            self.mock_client = MockTypesenseClient()
    
    def create_collection(self, collection_name: str) -> bool:
        """Create a collection"""
        if self.use_mock:
            return self.mock_client.create_collection(collection_name)
        else:
            return self.real_client.create_collection(collection_name)
    
    def index_document(self, collection_name: str, document: Document) -> bool:
        """Index a document"""
        if self.use_mock:
            return self.mock_client.index_document(collection_name, document)
        else:
            return self.real_client.index_document(collection_name, document)
    
    def search_documents(self, collection_name: str, query: str, 
                        num_results: int = 10, search_fields: List[str] = None) -> List[SearchResult]:
        """Search documents"""
        if self.use_mock:
            return self.mock_client.search_documents(collection_name, query, num_results, search_fields)
        else:
            return self.real_client.search_documents(collection_name, query, num_results, search_fields)
    
    def delete_document(self, collection_name: str, document_id: str) -> bool:
        """Delete a document"""
        if self.use_mock:
            return self.mock_client.delete_document(collection_name, document_id)
        else:
            return self.real_client.delete_document(collection_name, document_id)
    
    def get_collection_stats(self, collection_name: str) -> Dict:
        """Get collection statistics"""
        if self.use_mock:
            return self.mock_client.get_collection_stats(collection_name)
        else:
            return self.real_client.get_collection_stats(collection_name)

# ============================================================================
# DOCUMENT PROCESSOR
# ============================================================================

class DocumentProcessor:
    """Process documents for indexing with enhanced error handling"""
    
    def __init__(self, chunk_size: int = None, chunk_overlap: int = None):
        self.chunk_size = chunk_size or DEFAULT_CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or DEFAULT_CHUNK_OVERLAP
        
        # Initialize embeddings for semantic chunking
        self.use_semantic_chunking = False
        self.embeddings = None
        self.text_splitter = None
        
        if LANGCHAIN_AVAILABLE:
            try:
                embeddings_model = get_env_var('EMBEDDINGS_MODEL', 'text-embedding-3-small')
                embeddings_base = get_env_var('EMBEDDINGS_API_BASE')
                embeddings_key = get_env_var('EMBEDDINGS_API_KEY')
                
                if embeddings_base and embeddings_key:
                    self.embeddings = OpenAIEmbeddings(
                        model=embeddings_model,
                        openai_api_base=embeddings_base,
                        openai_api_key=embeddings_key
                    )
                    
                    self.text_splitter = SemanticChunker(
                        embeddings=self.embeddings,
                        breakpoint_threshold_type="interquartile",
                        breakpoint_threshold_amount=0.05,
                        number_of_chunks=None
                    )
                    self.use_semantic_chunking = True
                    logger.info("✅ Semantic chunking enabled")
                else:
                    logger.warning("⚠️  Embedding credentials not found - using simple chunking")
                    
            except Exception as e:
                logger.warning(f"⚠️  Semantic chunking failed: {e}")
                
        if not self.use_semantic_chunking:
            logger.info("🔄 Using simple text chunking")
    
    def _simple_chunk_text(self, text: str) -> List[str]:
        """Enhanced simple text chunking with sentence boundary awareness"""
        if not text.strip():
            return []
        
        # First, try to split by sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        chunks = []
        current_chunk = []
        current_size = 0
        
        for sentence in sentences:
            sentence_size = len(sentence)
            
            # If adding this sentence would exceed chunk size, finalize current chunk
            if current_size + sentence_size > self.chunk_size and current_chunk:
                chunks.append('. '.join(current_chunk) + '.')
                current_chunk = []
                current_size = 0
            
            # If a single sentence is too long, split it by words
            if sentence_size > self.chunk_size:
                words = sentence.split()
                word_chunk = []
                word_size = 0
                
                for word in words:
                    if word_size + len(word) > self.chunk_size and word_chunk:
                        current_chunk.append(' '.join(word_chunk))
                        if current_chunk:
                            chunks.append('. '.join(current_chunk) + '.')
                            current_chunk = []
                            current_size = 0
                        word_chunk = []
                        word_size = 0
                    
                    word_chunk.append(word)
                    word_size += len(word) + 1
                
                if word_chunk:
                    current_chunk.append(' '.join(word_chunk))
                    current_size += word_size
            else:
                current_chunk.append(sentence)
                current_size += sentence_size
        
        # Add final chunk
        if current_chunk:
            chunks.append('. '.join(current_chunk) + '.')
        
        return chunks
    
    def process_docx_file(self, file_path: str) -> List[Document]:
        """Process a DOCX file with enhanced error handling"""
        if not DOCX_AVAILABLE:
            logger.error("❌ DOCX processing not available - install python-docx")
            return []
        
        try:
            # Read the DOCX file
            doc = docx.Document(file_path)
            
            # Extract text content
            content = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    content.append(text)
            
            # Extract table content
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                    if row_text:
                        content.append(' | '.join(row_text))
            
            full_text = '\n'.join(content)
            
            if not full_text.strip():
                logger.warning(f"⚠️  No text content found in {file_path}")
                return []
            
            return self._create_document_chunks(file_path, full_text, 'docx')
            
        except Exception as e:
            logger.error(f"❌ Failed to process DOCX file {file_path}: {e}")
            return []
    
    def process_text_file(self, file_path: str) -> List[Document]:
        """Process a text file with enhanced error handling"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
            content = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                logger.error(f"❌ Failed to read {file_path} with any encoding")
                return []
            
            if not content.strip():
                logger.warning(f"⚠️  No text content found in {file_path}")
                return []
            
            return self._create_document_chunks(file_path, content, 'txt')
            
        except Exception as e:
            logger.error(f"❌ Failed to process text file {file_path}: {e}")
            return []
    
    def _create_document_chunks(self, file_path: str, content: str, file_type: str) -> List[Document]:
        """Create document chunks from content"""
        file_name = Path(file_path).stem
        
        # Split text into chunks
        if self.use_semantic_chunking:
            try:
                chunks = self.text_splitter.split_text(content)
            except Exception as e:
                logger.warning(f"⚠️  Semantic chunking failed, using simple chunking: {e}")
                chunks = self._simple_chunk_text(content)
        else:
            chunks = self._simple_chunk_text(content)
        
        if not chunks:
            logger.warning(f"⚠️  No chunks created from {file_path}")
            return []
        
        # Create documents for each chunk
        documents = []
        for i, chunk in enumerate(chunks):
            if not chunk.strip():
                continue
                
            doc_id = self._generate_doc_id(file_path, i)
            
            # Calculate token count
            try:
                token_count = count_tokens_enhanced(chunk)['tokens']
            except Exception:
                token_count = len(chunk) // 4  # Fallback estimation
            
            document = Document(
                id=doc_id,
                title=f"{file_name} - Phần {i+1}",
                content=chunk,
                file_path=file_path,
                file_type=file_type,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                chunk_index=i,
                total_chunks=len(chunks),
                metadata={
                    'original_file': file_name,
                    'chunk_length': len(chunk),
                    'token_count': token_count,
                    'processing_method': 'semantic' if self.use_semantic_chunking else 'simple'
                }
            )
            documents.append(document)
        
        logger.info(f"✅ Created {len(documents)} chunks from {file_path}")
        return documents
    
    def _generate_doc_id(self, file_path: str, chunk_index: int) -> str:
        """Generate a unique document ID"""
        path_hash = hashlib.md5(file_path.encode()).hexdigest()[:8]
        return f"{path_hash}_{chunk_index}"

# ============================================================================
# QA ENGINE
# ============================================================================

class QAEngine:
    """Q&A engine using Typesense for document retrieval"""
    
    def __init__(self, collection_name: str = None):
        self.collection_name = collection_name or DOCUMENTS_COLLECTION
        self.typesense_client = EnhancedTypesenseClient()
        self.document_processor = DocumentProcessor()
        self.llm_router = create_router_from_env()
        
        # Initialize collection
        self.typesense_client.create_collection(self.collection_name)
        
        # Conversation history
        self.conversation_history = []
    
    def index_document_file(self, file_path: str) -> bool:
        """Index a document file"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                return False
            
            # Process the file based on its type
            if file_path.lower().endswith('.docx'):
                documents = self.document_processor.process_docx_file(file_path)
            elif file_path.lower().endswith('.txt'):
                documents = self.document_processor.process_text_file(file_path)
            else:
                print(f"❌ Unsupported file type: {file_path}")
                return False
            
            # Index all document chunks
            success_count = 0
            for document in documents:
                if self.typesense_client.index_document(self.collection_name, document):
                    success_count += 1
            
            print(f"✅ Indexed {success_count}/{len(documents)} chunks from {file_path}")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ Failed to index file {file_path}: {e}")
            return False
    
    def index_directory(self, directory_path: str, file_extensions: List[str] = None) -> int:
        """Index all supported files in a directory"""
        if file_extensions is None:
            file_extensions = ['.docx', '.txt']
        
        indexed_count = 0
        
        try:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in file_extensions):
                        file_path = os.path.join(root, file)
                        if self.index_document_file(file_path):
                            indexed_count += 1
            
            print(f"✅ Successfully indexed {indexed_count} files from {directory_path}")
            return indexed_count
            
        except Exception as e:
            print(f"❌ Failed to index directory {directory_path}: {e}")
            return 0
    
    def search_documents(self, query: str, num_results: int = 5) -> List[SearchResult]:
        """Search for relevant documents"""
        return self.typesense_client.search_documents(
            self.collection_name, 
            query, 
            num_results=num_results
        )
    
    def ask_question(self, question: str, num_context_docs: int = 3, 
                    use_conversation_history: bool = True) -> QAResponse:
        """Ask a question and get an answer based on indexed documents"""
        try:
            # Search for relevant documents
            search_results = self.search_documents(question, num_context_docs)
            
            if not search_results:
                return QAResponse(
                    question=question,
                    answer="Xin lỗi, tôi không tìm thấy tài liệu nào liên quan đến câu hỏi của bạn.",
                    sources=[],
                    model_used="",
                    tokens_used=0,
                    confidence=0.0,
                    timestamp=datetime.now()
                )
            
            # Prepare context from search results
            context_parts = []
            for i, result in enumerate(search_results):
                context_parts.append(f"Tài liệu {i+1}: {result.document.title}")
                context_parts.append(f"Nội dung: {result.document.content}")
                context_parts.append("")  # Empty line for separation
            
            context = "\n".join(context_parts)
            
            # Prepare prompt
            if use_conversation_history and self.conversation_history:
                # Format conversation history
                history_parts = []
                for entry in self.conversation_history[-5:]:  # Last 5 exchanges
                    history_parts.append(f"Q: {entry['question']}")
                    history_parts.append(f"A: {entry['answer']}")
                    history_parts.append("")
                
                history = "\n".join(history_parts)
                prompt = CONTEXTUAL_QA_PROMPT_TEMPLATE.format(
                    context=context,
                    history=history,
                    question=question
                )
            else:
                prompt = QA_PROMPT_TEMPLATE.format(
                    context=context,
                    question=question
                )
            
            # Get answer from LLM
            try:
                response = self.llm_router.make_request(prompt, input="")
                
                if not response:
                    answer = "Xin lỗi, không nhận được phản hồi từ hệ thống AI."
                    model_used = ""
                    tokens_used = 0
                elif not response.get('success'):
                    answer = f"Xin lỗi, hệ thống AI gặp lỗi: {response.get('error', 'Unknown error')}"
                    model_used = response.get('model', '')
                    tokens_used = 0
                elif 'response' in response:
                    answer = response['response']
                    model_used = response.get('model', '')
                    tokens_used = response.get('total_tokens', 0)
                else:
                    answer = "Xin lỗi, định dạng phản hồi từ AI không hợp lệ."
                    model_used = response.get('model', '')
                    tokens_used = 0
                    
            except Exception as e:
                logger.error(f"Error calling LLM: {e}")
                answer = f"Xin lỗi, đã có lỗi xảy ra khi gọi AI: {str(e)}"
                model_used = ""
                tokens_used = 0
            
            # Calculate confidence based on search scores
            avg_score = sum(result.score for result in search_results) / len(search_results)
            confidence = min(avg_score / 100.0, 1.0)  # Normalize to 0-1
            
            # Create QA response
            qa_response = QAResponse(
                question=question,
                answer=answer,
                sources=search_results,
                model_used=model_used,
                tokens_used=tokens_used,
                confidence=confidence,
                timestamp=datetime.now()
            )
            
            # Add to conversation history
            if use_conversation_history:
                self.conversation_history.append({
                    'question': question,
                    'answer': response['text'],
                    'timestamp': datetime.now()
                })
            
            return qa_response
            
        except Exception as e:
            print(f"❌ Failed to process question: {e}")
            return QAResponse(
                question=question,
                answer=f"Xin lỗi, đã có lỗi xảy ra khi xử lý câu hỏi: {str(e)}",
                sources=[],
                model_used="",
                tokens_used=0,
                confidence=0.0,
                timestamp=datetime.now()
            )
    
    def get_collection_stats(self) -> Dict:
        """Get statistics about the document collection"""
        return self.typesense_client.get_collection_stats(self.collection_name)
    
    def clear_conversation_history(self):
        """Clear the conversation history"""
        self.conversation_history = []
        print("🧹 Conversation history cleared")
    
    def export_conversation_history(self, file_path: str):
        """Export conversation history to JSON file"""
        try:
            history_data = []
            for entry in self.conversation_history:
                history_data.append({
                    'question': entry['question'],
                    'answer': entry['answer'],
                    'timestamp': entry['timestamp'].isoformat()
                })
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Conversation history exported to {file_path}")
            
        except Exception as e:
            print(f"❌ Failed to export conversation history: {e}")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def create_qa_engine(collection_name: str = None) -> QAEngine:
    """Create a new QA engine instance"""
    return QAEngine(collection_name)

def quick_qa(question: str, documents_path: str = None, collection_name: str = None) -> str:
    """Quick Q&A function for simple use cases"""
    qa_engine = create_qa_engine(collection_name)
    
    # Index documents if path provided
    if documents_path and os.path.exists(documents_path):
        if os.path.isdir(documents_path):
            qa_engine.index_directory(documents_path)
        else:
            qa_engine.index_document_file(documents_path)
    
    # Ask question
    response = qa_engine.ask_question(question)
    return response.answer

def batch_qa(questions: List[str], documents_path: str = None, 
            collection_name: str = None) -> List[QAResponse]:
    """Process multiple questions at once"""
    qa_engine = create_qa_engine(collection_name)
    
    # Index documents if path provided
    if documents_path and os.path.exists(documents_path):
        if os.path.isdir(documents_path):
            qa_engine.index_directory(documents_path)
        else:
            qa_engine.index_document_file(documents_path)
    
    # Process all questions
    responses = []
    for question in questions:
        response = qa_engine.ask_question(question)
        responses.append(response)
    
    return responses
