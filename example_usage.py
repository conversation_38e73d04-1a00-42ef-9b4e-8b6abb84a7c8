#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example file showing how to use the AI functions from ai.py
Ví dụ sử dụng các function AI từ file ai.py
"""

from ai import extract_sections_from_docx, extract_sections_from_text, read_docx
import json


def example_usage_with_docx_file():
    """Ví dụ sử dụng với file Word"""
    print("=== VÍ DỤ 1: SỬ DỤNG VỚI FILE WORD ===")
    
    # Đường dẫn đến file Word
    docx_file = "1.docx"  # Thay đổi đường dẫn này
    
    try:
        # Gọi function để trích xuất thông tin
        results = extract_sections_from_docx(
            word_file_path=docx_file,
            max_tokens=7500,
            verbose=True
        )
        
        print(f"\nĐã nhận được {len(results)} kết quả:")
        for i, result in enumerate(results):
            print(f"\n--- KẾT QUẢ CHUNK {i+1} ---")
            print(result)
            
    except Exception as e:
        print(f"Lỗi: {e}")


def example_usage_with_text():
    """Ví dụ sử dụng với text content"""
    print("\n\n=== VÍ DỤ 2: SỬ DỤNG VỚI TEXT CONTENT ===")
    
    # Ví dụ text content
    sample_text = """
    Ngày tháng: 15/06/2024
    
    Tên hoạt động: Hội thảo về trí tuệ nhân tạo
    
    Mục đích: 
    - Tìm hiểu về các ứng dụng AI trong giáo dục
    - Chia sẻ kinh nghiệm thực tiễn
    
    Chuẩn bị:
    - Máy chiếu
    - Tài liệu
    - Phòng họp cho 50 người
    
    Tiến hành:
    - 9:00 - 9:30: Đăng ký tham dự
    - 9:30 - 11:00: Phần thuyết trình chính
    - 11:00 - 11:15: Nghỉ giải lao
    - 11:15 - 12:00: Thảo luận nhóm
    
    Kết thúc:
    - Tổng kết nội dung
    - Đánh giá hiệu quả
    - Kế hoạch tiếp theo
    """
    
    try:
        # Gọi function để trích xuất thông tin từ text
        results = extract_sections_from_text(
            text_content=sample_text,
            max_tokens=5000,
            verbose=True
        )
        
        print(f"\nĐã nhận được {len(results)} kết quả:")
        for i, result in enumerate(results):
            print(f"\n--- KẾT QUẢ CHUNK {i+1} ---")
            print(result)
            
            # Thử parse JSON để kiểm tra format
            try:
                parsed = json.loads(result)
                print("\nParsed JSON thành công:")
                for key, value in parsed.items():
                    print(f"  {key}: {value}")
            except json.JSONDecodeError:
                print("Không thể parse JSON - có thể cần điều chỉnh prompt")
                
    except Exception as e:
        print(f"Lỗi: {e}")


def example_read_docx_only():
    """Ví dụ chỉ đọc file Word mà không phân tích AI"""
    print("\n\n=== VÍ DỤ 3: CHỈ ĐỌC FILE WORD ===")
    
    docx_file = "1.docx"  # Thay đổi đường dẫn này
    
    try:
        content = read_docx(docx_file)
        print(f"Nội dung file (first 500 chars):")
        print(content[:500] + "..." if len(content) > 500 else content)
        print(f"\nTổng cộng: {len(content)} ký tự")
        
    except Exception as e:
        print(f"Lỗi khi đọc file: {e}")


if __name__ == "__main__":
    print("DEMO SỬ DỤNG CÁC FUNCTION TỪ ai.py")
    print("=" * 50)
    
    # Chạy các ví dụ
    #example_usage_with_text()  # Chạy trước vì không cần file
    
    # Uncomment các dòng dưới nếu bạn có file Word để test
    # example_read_docx_only()
    example_usage_with_docx_file()
    
    print("\n" + "=" * 50)
    print("HƯỚNG DẪN SỬ DỤNG:")
    print("1. Import: from ai import extract_sections_from_docx, extract_sections_from_text")
    print("2. Với file Word: results = extract_sections_from_docx('path/to/file.docx')")
    print("3. Với text: results = extract_sections_from_text('your text content')")
    print("4. Kết quả trả về là list các JSON string từ AI")
