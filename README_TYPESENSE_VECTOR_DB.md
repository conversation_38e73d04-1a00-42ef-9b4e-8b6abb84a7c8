# Typesense Vector Database Library

Thư viện sử dụng Typesense làm vector database cho việc embedding văn bản và tìm kiếm ngữ nghĩa.

## Tính năng

- ✅ **Import dữ liệu từ file DOCX**: <PERSON><PERSON><PERSON> và chia nhỏ văn bản thành chunks
- ✅ **Vector Embedding**: Sử dụng OpenAI embeddings để tạo vector representations
- ✅ **Tìm kiếm ngữ nghĩa**: Tìm kiếm documents dựa trên similarity
- ✅ **Hệ thống Q&A**: Trả lời câu hỏi dựa trên context từ documents
- ✅ **LLM Router Integration**: Sử dụng llm_router để xử lý AI
- ✅ **Quản lý dữ liệu**: Xóa, thống kê collection

## Cài đặt

### 1. Cài đặt Typesense Server

#### Sử dụng Docker (Khuyến nghị)
```bash
docker run -p 8108:8108 -v/tmp/typesense-data:/data typesense/typesense:0.25.2 \
  --data-dir /data --api-key=xyz --enable-cors
```

#### Hoặc tải về binary
```bash
# Linux
curl -O https://dl.typesense.org/releases/0.25.2/typesense-server-0.25.2-linux-amd64.tar.gz
tar -xzf typesense-server-0.25.2-linux-amd64.tar.gz
./typesense-server --data-dir=/tmp/typesense-data --api-key=xyz --enable-cors

# macOS
curl -O https://dl.typesense.org/releases/0.25.2/typesense-server-0.25.2-darwin-amd64.tar.gz
tar -xzf typesense-server-0.25.2-darwin-amd64.tar.gz
./typesense-server --data-dir=/tmp/typesense-data --api-key=xyz --enable-cors
```

### 2. Cài đặt Python dependencies

```bash
pip install typesense>=0.21.0 langchain langchain-openai python-docx python-dotenv
```

### 3. Cấu hình Environment Variables

Tạo file `.env` với nội dung:

```env
# Typesense Configuration
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=xyz
TYPESENSE_TIMEOUT=60

# OpenAI Embeddings Configuration
EMBEDDINGS_MODEL=text-embedding-ada-002
EMBEDDINGS_API_BASE=https://api.openai.com/v1
EMBEDDINGS_API_KEY=your_openai_api_key

# LLM Router Configuration (xem llm_config.json)
```

## Sử dụng

### 1. Import dữ liệu từ file DOCX

```python
from typesense_vector_db import TypesenseVectorDB

# Khởi tạo database
db = TypesenseVectorDB(collection_name="my_documents")

# Import file DOCX
result = db.import_docx_to_typesense(
    file_path="document.docx",
    title="Tài liệu quan trọng",
    metadata={"category": "education", "author": "John Doe"}
)

print(result)
```

### 2. Tìm kiếm documents tương tự

```python
# Tìm kiếm dựa trên vector similarity
result = db.search_similar_documents(
    query="hoạt động giáo dục",
    limit=5,
    threshold=0.7
)

for doc in result["documents"]:
    print(f"File: {doc['source_file']}")
    print(f"Similarity: {doc['similarity']:.3f}")
    print(f"Content: {doc['content'][:100]}...")
    print("-" * 50)
```

### 3. Hệ thống hỏi đáp

```python
# Trả lời câu hỏi dựa trên context
result = db.search_and_answer(
    question="Mục đích của hoạt động giáo dục là gì?",
    limit=3,
    threshold=0.6
)

if result["success"]:
    print(f"Câu trả lời: {result['answer']}")
    print(f"Confidence: {result['confidence']:.3f}")
    print(f"LLM sử dụng: {result['llm_used']}")
    
    print("\nNguồn tham khảo:")
    for source in result["sources"]:
        print(f"- {source['source_file']} (similarity: {source['similarity']:.3f})")
```

### 4. Quản lý collection

```python
# Lấy thống kê
stats = db.get_collection_stats()
print(f"Tổng documents: {stats['total_documents']}")
print(f"Files: {stats['files_stats']}")

# Xóa documents từ một file
result = db.delete_documents_by_file("old_document.docx")
print(f"Đã xóa: {result['deleted_count']} documents")
```

## Chạy Test

```bash
python qa_test.py
```

Test sẽ kiểm tra:
- ✅ Kết nối Typesense
- ✅ Import file DOCX
- ✅ Tìm kiếm documents
- ✅ Hệ thống Q&A
- ✅ Quản lý collection

## Cấu trúc Collection

Typesense collection có schema:

```json
{
  "name": "documents",
  "fields": [
    {"name": "id", "type": "string"},
    {"name": "content", "type": "string"},
    {"name": "title", "type": "string", "optional": true},
    {"name": "source_file", "type": "string", "optional": true},
    {"name": "chunk_index", "type": "int32", "optional": true},
    {"name": "metadata", "type": "object", "optional": true},
    {"name": "embedding", "type": "float[]", "num_dim": 1536},
    {"name": "created_at", "type": "int64"},
    {"name": "content_hash", "type": "string"}
  ]
}
```

## API Reference

### TypesenseVectorDB Class

#### `__init__(collection_name: str = "documents")`
Khởi tạo kết nối Typesense và tạo collection nếu chưa tồn tại.

#### `import_docx_to_typesense(file_path: str, title: str = None, metadata: Dict = None) -> Dict`
Import dữ liệu từ file DOCX vào Typesense.

**Parameters:**
- `file_path`: Đường dẫn đến file Word
- `title`: Tiêu đề tài liệu (optional)
- `metadata`: Metadata bổ sung (optional)

**Returns:** Dict với thông tin kết quả import

#### `search_similar_documents(query: str, limit: int = 10, threshold: float = 0.7) -> Dict`
Tìm kiếm documents tương tự dựa trên vector similarity.

**Parameters:**
- `query`: Câu hỏi/truy vấn
- `limit`: Số lượng kết quả tối đa
- `threshold`: Ngưỡng similarity (0-1)

**Returns:** Dict với danh sách documents tìm được

#### `search_and_answer(question: str, limit: int = 5, threshold: float = 0.7) -> Dict`
Tìm kiếm và trả lời câu hỏi sử dụng LLM.

**Parameters:**
- `question`: Câu hỏi
- `limit`: Số documents tối đa để tham khảo
- `threshold`: Ngưỡng similarity

**Returns:** Dict với câu trả lời và thông tin nguồn

#### `delete_documents_by_file(source_file: str) -> Dict`
Xóa tất cả documents từ một file cụ thể.

#### `get_collection_stats() -> Dict`
Lấy thống kê về collection.

## Troubleshooting

### Lỗi kết nối Typesense
- Kiểm tra Typesense server đã chạy chưa
- Kiểm tra cấu hình TYPESENSE_* trong .env
- Kiểm tra firewall/port 8108

### Lỗi embedding
- Kiểm tra EMBEDDINGS_API_KEY
- Kiểm tra kết nối internet
- Kiểm tra quota OpenAI API

### Lỗi LLM Router
- Kiểm tra file llm_config.json
- Kiểm tra API keys trong config
- Xem log để biết LLM nào đang được sử dụng

## Ví dụ hoàn chỉnh

Xem file `qa_test.py` để có ví dụ hoàn chỉnh về cách sử dụng tất cả tính năng.
