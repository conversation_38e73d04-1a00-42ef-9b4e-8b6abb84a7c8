#!/usr/bin/env python3
"""
Test script cho LLMRouter đã được tối ưu
Kiể<PERSON> tra việc load config chỉ từ database
"""

import sys
import os
from datetime import date

def test_database_only_config():
    """Test việc load config chỉ từ database"""
    print("🧪 Testing LLMRouter với database-only config...")
    
    try:
        from llm_router import LLMRouter, create_router_from_env
        
        # Test 1: Khởi tạo router với database
        print("\n1️⃣ Test khởi tạo router...")
        router = LLMRouter()
        print(f"✅ Router khởi tạo thành công với {len(router.llm_configs)} configs")
        
        # Test 2: Kiể<PERSON> tra configs được load từ database
        print("\n2️⃣ Test load configs từ database...")
        if router.llm_configs:
            for config in router.llm_configs:
                print(f"   - {config.name}: {config.provider}/{config.model}")
        else:
            print("   ⚠️  <PERSON>hông có config nào được load")
        
        # Test 3: Test status
        print("\n3️⃣ Test status...")
        router.print_status()
        
        # Test 4: Test save config
        print("\n4️⃣ Test save config...")
        router.save_config()
        print("✅ Save config thành công")
        
        # Test 5: Test utility function
        print("\n5️⃣ Test utility function...")
        router2 = create_router_from_env()
        print(f"✅ Utility function tạo router với {len(router2.llm_configs)} configs")
        
        # Test 6: Test database stats
        print("\n6️⃣ Test database stats...")
        stats = router.get_database_stats(7)
        if stats:
            print(f"✅ Database stats: {len(stats)} entries")
        else:
            print("ℹ️  Chưa có stats trong database")
        
        print("\n🎉 Tất cả tests đã pass!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling khi database không khả dụng"""
    print("\n🧪 Testing error handling...")
    
    try:
        # Backup original DATABASE_AVAILABLE
        import llm_router
        original_db_available = llm_router.DATABASE_AVAILABLE
        
        # Temporarily disable database
        llm_router.DATABASE_AVAILABLE = False
        
        try:
            router = llm_router.LLMRouter()
            print("❌ Should have raised exception when database not available")
            return False
        except Exception as e:
            print(f"✅ Correctly raised exception: {e}")
        
        # Restore original value
        llm_router.DATABASE_AVAILABLE = original_db_available
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_config_operations():
    """Test các operations với config"""
    print("\n🧪 Testing config operations...")
    
    try:
        from llm_router import LLMRouter, LLMConfig
        
        router = LLMRouter()
        original_count = len(router.llm_configs)
        
        # Test add LLM
        print("\n1️⃣ Test add LLM...")
        test_config = LLMConfig(
            name="Test LLM",
            provider="openai",
            model="gpt-3.5-turbo",
            api_base_url="https://api.openai.com/v1",
            api_token="test-token",
            max_calls=10,
            remaining_calls=10
        )
        
        router.add_llm(test_config)
        if len(router.llm_configs) == original_count + 1:
            print("✅ Add LLM thành công")
        else:
            print("❌ Add LLM thất bại")
            return False
        
        # Test remove LLM
        print("\n2️⃣ Test remove LLM...")
        router.remove_llm("Test LLM")
        if len(router.llm_configs) == original_count:
            print("✅ Remove LLM thành công")
        else:
            print("❌ Remove LLM thất bại")
            return False
        
        # Test enable/disable LLM
        if router.llm_configs:
            test_name = router.llm_configs[0].name
            
            print(f"\n3️⃣ Test disable/enable LLM: {test_name}...")
            router.disable_llm(test_name)
            router.enable_llm(test_name)
            print("✅ Enable/disable LLM thành công")
        
        # Test reset calls
        print("\n4️⃣ Test reset calls...")
        router.reset_all_calls()
        print("✅ Reset calls thành công")
        
        return True
        
    except Exception as e:
        print(f"❌ Config operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Bắt đầu test LLMRouter đã tối ưu...")
    print("="*60)
    
    tests = [
        test_database_only_config,
        test_error_handling,
        test_config_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_func.__name__} PASSED")
            else:
                print(f"❌ {test_func.__name__} FAILED")
        except Exception as e:
            print(f"❌ {test_func.__name__} ERROR: {e}")
        
        print("-" * 40)
    
    print(f"\n📊 KẾT QUẢ: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 TẤT CẢ TESTS ĐÃ PASS!")
        return True
    else:
        print("⚠️  MỘT SỐ TESTS THẤT BẠI!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
