#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI application để xử lý file Word sử dụng AI với LLM Router
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
import tempfile
import os
import json
from typing import List, Optional
import shutil

from ai import extract_sections_from_docx, get_env_var, extract_sections_from_docx_with_router
from llm_router import LLMRouter, create_router_from_env

# Khởi tạo FastAPI app
app = FastAPI(
    title="Word Document Processor",
    description="API để xử lý file Word và trích xuất thông tin sử dụng AI với LLM Router",
    version="2.0.0"
)

# Available models từ các providers
AVAILABLE_MODELS = {
    "github": ["openai/gpt-4.1", "openai/gpt-4.1-mini"],
    "groq": ["llama-3.3-70b-versatile", "meta-llama/llama-4-maverick-17b-128e-instruct"],
    "gemini": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-2.0-flash-exp", "gemini-exp-1206"],
    "localai": []  # Will be loaded from env
}

# Pydantic models
class ProcessingResponse(BaseModel):
    """Response model cho kết quả xử lý"""
    success: bool
    message: str
    results: List[dict]
    file_info: Optional[dict] = None

class ChunkResult(BaseModel):
    """Model cho kết quả từng chunk"""
    chunk_index: int
    success: bool
    data: Optional[dict] = None
    raw_text: str
    error: Optional[str] = None

class ProcessingRequest(BaseModel):
    """Request model cho xử lý file"""
    max_tokens: Optional[int] = None  # DEPRECATED - will use LLM config
    verbose: Optional[bool] = False

class ModelSelection(BaseModel):
    """Model selection options"""
    provider: str  # 'github' or 'groq'
    model: str    # specific model name

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Word Document Processor API với LLM Router",
        "version": "2.0.0",
        "features": [
            "Upload và xử lý file Word (.docx)",
            "Trích xuất thông tin sử dụng AI",
            "Hỗ trợ multiple LLM providers (OpenAI, Groq, GitHub, Gemini)",
            "LLM Router với load balancing",
            "Token limits theo LLM config"
        ],
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Kiểm tra trạng thái API"""
    return {"status": "healthy", "message": "API đang hoạt động bình thường"}

@app.post("/process-docx", response_model=ProcessingResponse)
async def process_docx_upload(
    file: UploadFile = File(..., description="File Word cần xử lý (.docx)"),
    max_tokens: Optional[int] = Form(None, description="DEPRECATED - Giá trị sẽ được lấy từ LLM config"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    model: Optional[str] = Form(None, description="Model AI để sử dụng (optional)")
):
    """
    Upload và xử lý file Word
    
    Args:
        file: File Word upload (.docx)
        max_tokens: DEPRECATED - Giá trị sẽ được lấy từ LLM config
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        model: Model AI để sử dụng (optional)
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ AI
    """
    
    # Warning about deprecated parameter
    if max_tokens is not None:
        print(f"⚠️  Warning: max_tokens parameter is deprecated. Using max_input_tokens from LLM config instead.")
    
    # Kiểm tra loại file
    if not file.filename.lower().endswith('.docx'):
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": "Chỉ hỗ trợ file Word (.docx). Vui lòng upload file đúng định dạng.",
                "code": 400
            }
        )
    
    # Validate model if provided
    if model:
        all_models = []
        for provider_models in AVAILABLE_MODELS.values():
            all_models.extend(provider_models)
        
        if model not in all_models:
            raise HTTPException(
                status_code=400,
                detail=f"Model '{model}' không được hỗ trợ. Các model khả dụng: {', '.join(all_models)}"
            )
    
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name
        file_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else os.path.getsize(temp_file_path)
        }

        # --- Token limit check ---
        from ai import read_docx, count_tokens, get_env_var
        try:
            content = read_docx(temp_file_path)
        except Exception as e:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": f"Không đọc được file Word: {str(e)}",
                    "code": 400
                }
            )
        max_tokens_limit = get_env_var('WORD_FILE_MAX_TOKENS', 12000)
        try:
            max_tokens_limit = int(str(max_tokens_limit).split('#')[0].strip())
        except Exception:
            max_tokens_limit = 12000
        num_tokens = count_tokens(content)
        if num_tokens > max_tokens_limit:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": f"File Word vượt quá giới hạn {max_tokens_limit} tokens (thực tế: {num_tokens} tokens). Vui lòng kiểm tra lại nội dung file.",
                    "code": 400
                }
            )
        # --- End token limit check ---

        import asyncio
        from fastapi.concurrency import run_in_threadpool
        results = await run_in_threadpool(
            extract_sections_from_docx,
            temp_file_path,
            max_tokens,
            verbose,
            model
        )
        return ProcessingResponse(
            success=True,
            message=f"Đã xử lý thành công file '{file.filename}' với {len(results)} chunk(s)",
            results=results,
            file_info=file_info
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Lỗi khi xử lý file: {str(e)}",
                "code": 500
            }
        )
    finally:
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

@app.post("/process-docx-router", response_model=ProcessingResponse)
async def process_docx_with_router(
    file: UploadFile = File(..., description="File Word cần xử lý (.docx)"),
    max_tokens: Optional[int] = Form(None, description="DEPRECATED - Giá trị sẽ được lấy từ LLM config"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    reset_calls: bool = Form(False, description="Reset số lượt call của tất cả LLM trước khi xử lý")
):
    """
    Upload và xử lý file Word sử dụng LLM Router
    
    Args:
        file: File Word upload (.docx)
        max_tokens: DEPRECATED - Giá trị sẽ được lấy từ LLM config
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        reset_calls: Reset số lượt call của tất cả LLM trước khi xử lý
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ LLM Router với thông tin chi tiết về usage
    """
    
    # Warning about deprecated parameter
    if max_tokens is not None:
        print(f"⚠️  Warning: max_tokens parameter is deprecated. Using max_input_tokens from LLM config instead.")
    
    # Kiểm tra loại file
    if not file.filename.lower().endswith('.docx'):
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": "Chỉ hỗ trợ file Word (.docx). Vui lòng upload file đúng định dạng.",
                "code": 400
            }
        )
    
    # Tạo file tạm để lưu upload
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            # Copy nội dung file upload vào file tạm
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name

        # Lấy thông tin file
        file_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else os.path.getsize(temp_file_path)
        }

        # --- Token limit check ---
        from ai import read_docx, count_tokens, get_env_var
        try:
            content = read_docx(temp_file_path)
        except Exception as e:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": f"Không đọc được file Word: {str(e)}",
                    "code": 400
                }
            )
        max_tokens_limit = get_env_var('WORD_FILE_MAX_TOKENS', 12000)
        try:
            max_tokens_limit = int(str(max_tokens_limit).split('#')[0].strip())
        except Exception:
            max_tokens_limit = 12000
        num_tokens = count_tokens(content)
        if num_tokens > max_tokens_limit:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": f"File Word vượt quá giới hạn {max_tokens_limit} tokens (thực tế: {num_tokens} tokens). Vui lòng kiểm tra lại nội dung file.",
                    "code": 400
                }
            )
        # --- End token limit check ---

        # Khởi tạo router
        router = create_router_from_env()

        # Reset calls nếu được yêu cầu
        if reset_calls:
            router.reset_all_calls()
            print("✅ Đã reset tất cả số lượt call")

        # Gọi function AI với router (bất đồng bộ, tránh block event loop)
        import asyncio
        from fastapi.concurrency import run_in_threadpool
        results = await run_in_threadpool(
            extract_sections_from_docx_with_router,
            temp_file_path,
            max_tokens,
            verbose,
            router
        )
        # Extract meaningful chunk information from results
        total_chunks_processed = 0
        total_data_items = 0
        if results and len(results) > 0:
            result = results[0]
            total_chunks_processed = result.get('total_chunks', len(results))
            total_data_items = len(result.get('data', []))
        return ProcessingResponse(
            success=True,
            message=f"Đã xử lý thành công file '{file.filename}' với LLM Router. {total_chunks_processed} chunk(s) processed, {total_data_items} data item(s) extracted",
            results=results,
            file_info=file_info
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Lỗi khi xử lý file với Router: {str(e)}",
                "code": 500
            }
        )
    finally:
        # Xóa file tạm
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

@app.get("/models")
async def get_available_models():
    """Get list of available AI models"""
    return {
        "available_models": AVAILABLE_MODELS,
        "total_models": sum(len(models) for models in AVAILABLE_MODELS.values()),
        "providers": list(AVAILABLE_MODELS.keys())
    }

@app.get("/router-status")
async def get_router_status():
    """Lấy trạng thái hiện tại của LLM Router"""
    try:
        router = create_router_from_env()
        status = router.get_status()
        
        return {
            "success": True,
            "router_status": status,
            "message": f"Router có {status['available_llms']}/{status['total_llms']} LLM khả dụng"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy trạng thái router: {str(e)}"
        )

@app.post("/router-reset")
async def reset_router_calls():
    """Reset tất cả số lượt call của các LLM trong router"""
    try:
        router = create_router_from_env()
        router.reset_all_calls()
        
        return {
            "success": True,
            "message": "Đã reset tất cả số lượt call của các LLM",
            "router_status": router.get_status()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi reset router: {str(e)}"
        )

@app.get("/config")
async def get_config_info():
    """Lấy thông tin cấu hình hiện tại"""
    try:
        router = create_router_from_env()
        status = router.get_status()
        
        # Lấy thông tin max_input_tokens từ các LLM
        max_tokens_info = {}
        for llm in status['llms']:
            try:
                max_tokens_info[llm['name']] = router.get_max_input_tokens(llm['name'])
            except:
                max_tokens_info[llm['name']] = "N/A"
        
        return {
            "success": True,
            "available_models": AVAILABLE_MODELS,
            "llm_configs": status['llms'],
            "max_input_tokens": max_tokens_info,
            "default_max_input_tokens": router.get_max_input_tokens(),
            "message": "Cấu hình hiện tại"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy thông tin cấu hình: {str(e)}"
        )

@app.get("/database-stats")
async def get_database_stats(days: int = 7):
    """Lấy thống kê từ database"""
    try:
        router = create_router_from_env()
        
        if not router.use_database:
            return {
                "success": False,
                "message": "Database không được kích hoạt",
                "database_enabled": False
            }
        
        stats = router.get_database_stats(days)
        
        if not stats:
            return {
                "success": False,
                "message": "Không thể lấy thống kê database",
                "database_enabled": True
            }
        
        return {
            "success": True,
            "message": f"Thống kê {days} ngày qua",
            "database_enabled": True,
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy thống kê database: {str(e)}"
        )

@app.post("/database-reset")
async def reset_database_calls():
    """Reset daily calls trong database"""
    try:
        router = create_router_from_env()
        
        if not router.use_database:
            return {
                "success": False,
                "message": "Database không được kích hoạt",
                "database_enabled": False
            }
        
        success = router.reset_daily_calls_database()
        
        return {
            "success": success,
            "message": "Daily calls đã được reset trong database" if success else "Không thể reset daily calls",
            "database_enabled": True
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi reset database calls: {str(e)}"
        )

@app.post("/database-cleanup")
async def cleanup_database_logs(days_to_keep: int = 30):
    """Cleanup log cũ trong database"""
    try:
        router = create_router_from_env()
        
        if not router.use_database:
            return {
                "success": False,
                "message": "Database không được kích hoạt",
                "database_enabled": False
            }
        
        success = router.cleanup_old_database_logs(days_to_keep)
        
        return {
            "success": success,
            "message": f"Đã cleanup logs cũ hơn {days_to_keep} ngày" if success else "Không thể cleanup logs",
            "database_enabled": True,
            "days_kept": days_to_keep
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi cleanup database logs: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    
    # Lấy cấu hình từ environment
    host = get_env_var('HOST', '0.0.0.0')
    port = int(get_env_var('PORT', '8000'))
    
    print(f"🚀 Starting Word Document Processor API v2.0.0")
    print(f"📡 Server: http://{host}:{port}")
    print(f"📚 Docs: http://{host}:{port}/docs")
    print(f"🔧 Config endpoint: http://{host}:{port}/config")
    print(f"🤖 Router status: http://{host}:{port}/router-status")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,  # Enable auto-reload for development
        log_level="info"
    )