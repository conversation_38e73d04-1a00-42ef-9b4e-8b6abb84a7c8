#!/usr/bin/env python3
"""
Setup script để khởi tạo MySQL database cho LLM Router
"""

import sys
import os
import mysql.connector
from mysql.connector import Error

def check_mysql_connection(host='localhost', port=3306, user='root', password=''):
    """Kiểm tra kết nối MySQL"""
    try:
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password
        )
        
        if connection.is_connected():
            print("✅ Kết nối MySQL thành công")
            connection.close()
            return True
        else:
            print("❌ Không thể kết nối MySQL")
            return False
    
    except Error as e:
        print(f"❌ Lỗi kết nối MySQL: {e}")
        return False

def create_database_and_user():
    """Tạo database và user cho LLM Router"""
    print("🔧 Thiết lập MySQL Database cho LLM Router")
    print("=" * 50)
    
    # <PERSON><PERSON><PERSON> thông tin từ người dùng
    host = input("MySQL Host (localhost): ").strip() or "localhost"
    port = int(input("MySQL Port (3306): ").strip() or "3306")
    root_user = input("MySQL Root User (root): ").strip() or "root"
    root_password = input("MySQL Root Password: ").strip()
    
    db_name = input("Database Name (llm_router): ").strip() or "llm_router"
    db_user = input("App User Name (llm_user): ").strip() or "llm_user"
    db_password = input("App User Password: ").strip()
    
    try:
        # Kết nối với quyền root
        print(f"\n🔌 Kết nối đến MySQL server...")
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=root_user,
            password=root_password
        )
        
        cursor = connection.cursor()
        
        # Tạo database
        print(f"📊 Tạo database '{db_name}'...")
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ Database '{db_name}' đã được tạo")
        
        # Tạo user và cấp quyền
        if db_user != root_user:
            print(f"👤 Tạo user '{db_user}'...")
            cursor.execute(f"CREATE USER IF NOT EXISTS '{db_user}'@'%' IDENTIFIED BY '{db_password}'")
            cursor.execute(f"GRANT ALL PRIVILEGES ON {db_name}.* TO '{db_user}'@'%'")
            cursor.execute("FLUSH PRIVILEGES")
            print(f"✅ User '{db_user}' đã được tạo và cấp quyền")
        
        # Test kết nối với user mới
        connection.close()
        
        print(f"\n🧪 Test kết nối với user '{db_user}'...")
        test_connection = mysql.connector.connect(
            host=host,
            port=port,
            database=db_name,
            user=db_user,
            password=db_password,
            charset='utf8mb4'
        )
        
        if test_connection.is_connected():
            print("✅ Test kết nối thành công")
            test_connection.close()
        
        # Tạo file .env
        env_content = f"""# Database Configuration for LLM Router
DB_HOST={host}
DB_PORT={port}
DB_NAME={db_name}
DB_USER={db_user}
DB_PASSWORD={db_password}
DB_CHARSET=utf8mb4

# Application Settings
USE_DATABASE=true
LOG_LEVEL=INFO
CLEANUP_OLD_LOGS_DAYS=30

# LLM API Keys (update with your actual keys)
OPENAI_API_KEY=your-openai-api-key
GROQ_API_KEY=your-groq-api-key
GEMINI_API_KEY=your-gemini-api-key
GITHUB_TOKEN=your-github-token
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print(f"\n📝 Đã tạo file .env với cấu hình database")
        
        # Khởi tạo database schema
        print(f"\n🏗️  Khởi tạo database schema...")
        from database_manager import DatabaseManager, DatabaseConfig
        
        db_config = DatabaseConfig(
            host=host,
            port=port,
            database=db_name,
            user=db_user,
            password=db_password
        )
        
        db_manager = DatabaseManager(db_config)
        if db_manager.connection:
            print("✅ Database schema đã được khởi tạo")
            
            # Đồng bộ configs từ JSON
            from llm_router import LLMRouter
            router = LLMRouter(use_database=False)
            if router.llm_configs:
                config_dicts = [config.to_dict() for config in router.llm_configs]
                db_manager.sync_llm_configs(config_dicts)
                print(f"✅ Đã đồng bộ {len(config_dicts)} LLM configs")
            
            db_manager.disconnect()
        
        print(f"\n🎉 Setup hoàn thành!")
        print(f"Database: {db_name}")
        print(f"User: {db_user}")
        print(f"Host: {host}:{port}")
        print(f"\n📋 Để sử dụng:")
        print(f"1. Cập nhật API keys trong file .env")
        print(f"2. Chạy: python daily_reset.py test")
        print(f"3. Chạy: python daily_reset.py sync")
        
        return True
        
    except Error as e:
        print(f"❌ Lỗi setup database: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def install_requirements():
    """Cài đặt các packages cần thiết"""
    print("📦 Cài đặt requirements...")
    
    requirements = [
        "mysql-connector-python",
        "python-dotenv"
    ]
    
    try:
        import subprocess
        for req in requirements:
            try:
                __import__(req.replace('-', '_'))
                print(f"✅ {req} đã được cài đặt")
            except ImportError:
                print(f"📦 Cài đặt {req}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", req])
                print(f"✅ {req} đã được cài đặt")
        
        return True
    except Exception as e:
        print(f"❌ Lỗi cài đặt requirements: {e}")
        return False

def test_setup():
    """Test setup"""
    print("🧪 Test setup...")
    
    try:
        from database_manager import DatabaseManager, create_database_config_from_env
        from llm_router import LLMRouter
        
        # Test database
        db_manager = DatabaseManager(create_database_config_from_env())
        if db_manager.connection:
            print("✅ Database connection OK")
            
            # Test router
            router = LLMRouter(use_database=True)
            print(f"✅ Router OK - {len(router.llm_configs)} configs loaded")
            
            # Test basic operations
            stats = db_manager.get_request_stats()
            print("✅ Database operations OK")
            
            db_manager.disconnect()
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 LLM Router MySQL Setup")
    print("=" * 30)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'test':
            success = test_setup()
            sys.exit(0 if success else 1)
        elif command == 'install':
            success = install_requirements()
            sys.exit(0 if success else 1)
        elif command == 'database':
            success = create_database_and_user()
            sys.exit(0 if success else 1)
    
    # Interactive setup
    print("Chọn thao tác:")
    print("1. Cài đặt packages cần thiết")
    print("2. Thiết lập MySQL database")
    print("3. Test setup")
    print("4. Tất cả (1 + 2 + 3)")
    
    choice = input("\nNhập lựa chọn (1-4): ").strip()
    
    if choice == '1':
        install_requirements()
    elif choice == '2':
        create_database_and_user()
    elif choice == '3':
        test_setup()
    elif choice == '4':
        if install_requirements():
            if create_database_and_user():
                test_setup()
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
