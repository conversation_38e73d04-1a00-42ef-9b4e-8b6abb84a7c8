#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test file cho Typesense Vector Database Library
Kiểm tra các chức năng import dữ liệu từ docx và tìm kiếm
"""

import os
import sys
import json
from typing import Dict, Any
from typesense_vector_db import TypesenseVectorDB

def print_separator(title: str):
    """In dòng phân cách với tiêu đề"""
    print("\n" + "="*80)
    print(f" {title} ")
    print("="*80)

def print_result(result: Dict[str, Any], title: str = "Kết quả"):
    """In kết quả một cách đẹp mắt"""
    print(f"\n📊 {title}:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def test_connection():
    """Test kết nối Typesense"""
    print_separator("TEST KẾT NỐI TYPESENSE")
    
    try:
        # Khởi tạo TypesenseVectorDB
        db = TypesenseVectorDB(collection_name="test_documents")
        
        # L<PERSON>y thống kê collection
        stats = db.get_collection_stats()
        print_result(stats, "Thống kê Collection")
        
        if stats["success"]:
            print("✅ Kết nối Typesense thành công!")
            return db
        else:
            print("❌ Lỗi kết nối Typesense!")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi kết nối: {e}")
        return None

def test_import_docx(db: TypesenseVectorDB):
    """Test import file docx"""
    print_separator("TEST IMPORT FILE DOCX")
    
    # Tìm file docx trong thư mục hiện tại
    docx_files = [f for f in os.listdir('.') if f.endswith('.docx')]
    
    if not docx_files:
        print("❌ Không tìm thấy file .docx nào trong thư mục hiện tại")
        print("📝 Danh sách file có sẵn:")
        for f in os.listdir('.'):
            if os.path.isfile(f):
                print(f"   - {f}")
        return False
    
    print(f"📁 Tìm thấy {len(docx_files)} file(s) .docx:")
    for i, file in enumerate(docx_files):
        print(f"   {i+1}. {file}")
    
    # Test với file đầu tiên
    test_file = docx_files[0]
    print(f"\n🧪 Test import file: {test_file}")
    
    result = db.import_docx_to_typesense(
        file_path=test_file,
        title=f"Test Document - {test_file}",
        metadata={
            "test_run": True,
            "imported_by": "qa_test.py"
        }
    )
    
    print_result(result, "Kết quả Import")
    
    if result["success"]:
        print("✅ Import thành công!")
        return True
    else:
        print("❌ Import thất bại!")
        return False

def test_search_similar(db: TypesenseVectorDB):
    """Test tìm kiếm documents tương tự"""
    print_separator("TEST TÌM KIẾM DOCUMENTS TƯƠNG TỰ")
    
    # Các câu hỏi test
    test_queries = [
        "hoạt động giáo dục",
        "mục đích của bài học",
        "chuẩn bị gì cho tiết học",
        "tiến hành như thế nào",
        "ngày tháng"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Tìm kiếm: '{query}'")
        
        result = db.search_similar_documents(
            query=query,
            limit=3,
            threshold=0.5
        )
        
        if result["success"]:
            print(f"✅ Tìm thấy {result['total_found']} documents")
            
            for i, doc in enumerate(result["documents"]):
                print(f"\n📄 Document {i+1}:")
                print(f"   - File: {doc['source_file']}")
                print(f"   - Similarity: {doc['similarity']:.3f}")
                print(f"   - Content: {doc['content'][:100]}...")
        else:
            print(f"❌ Lỗi tìm kiếm: {result['error']}")

def test_qa_system(db: TypesenseVectorDB):
    """Test hệ thống hỏi đáp"""
    print_separator("TEST HỆ THỐNG HỎI ĐÁP")
    
    # Các câu hỏi test
    test_questions = [
        "Mùa thu thế nào ?"
    ]
    
    for question in test_questions:
        print(f"\n❓ Câu hỏi: {question}")
        
        result = db.search_and_answer(
            question=question,
            limit=3,
            threshold=0.5
        )
        
        if result["success"]:
            print(f"✅ Trả lời thành công!")
            print(f"🤖 LLM sử dụng: {result.get('llm_used', 'Unknown')}")
            print(f"📊 Confidence: {result.get('confidence', 0):.3f}")
            print(f"📚 Số documents tham khảo: {result.get('total_documents_found', 0)}")
            print(f"\n💬 Câu trả lời:")
            print(result["answer"])
            
            if result.get("sources"):
                print(f"\n📖 Nguồn tham khảo:")
                for i, source in enumerate(result["sources"]):
                    print(f"   {i+1}. {source['source_file']} (similarity: {source['similarity']:.3f})")
        else:
            print(f"❌ Lỗi trả lời: {result['error']}")

def test_collection_management(db: TypesenseVectorDB):
    """Test quản lý collection"""
    print_separator("TEST QUẢN LÝ COLLECTION")
    
    # Lấy thống kê hiện tại
    print("📊 Thống kê collection hiện tại:")
    stats = db.get_collection_stats()
    print_result(stats, "Thống kê Collection")
    
    # Test xóa documents theo file (nếu có)
    if stats["success"] and stats["files_stats"]:
        files = list(stats["files_stats"].keys())
        if files:
            test_file = files[0]
            print(f"\n🗑️  Test xóa documents từ file: {test_file}")
            
            # Hỏi người dùng có muốn xóa không
            response = input(f"Bạn có muốn xóa tất cả documents từ file '{test_file}'? (y/N): ")
            
            if response.lower() == 'y':
                delete_result = db.delete_documents_by_file(test_file)
                print_result(delete_result, "Kết quả xóa")
                
                # Lấy thống kê sau khi xóa
                print("\n📊 Thống kê sau khi xóa:")
                new_stats = db.get_collection_stats()
                print_result(new_stats, "Thống kê mới")
            else:
                print("⏭️  Bỏ qua test xóa")

def main():
    """Hàm chính để chạy tất cả tests"""
    print_separator("TYPESENSE VECTOR DATABASE - QA TEST")
    print("🧪 Bắt đầu test các chức năng của Typesense Vector Database")
    
    # Test 1: Kết nối
    db = test_connection()
    if not db:
        print("❌ Không thể kết nối Typesense. Dừng test.")
        return
    
    # # Test 2: Import docx
    # import_success = test_import_docx(db)
    
    # # Test 3: Tìm kiếm (chỉ chạy nếu có dữ liệu)
    # stats = db.get_collection_stats()
    # if stats["success"] and stats["total_documents"] > 0:
    #     test_search_similar(db)
    #     test_qa_system(db)
    # else:
    #     print("\n⚠️  Không có dữ liệu để test tìm kiếm")
    
    # # Test 4: Quản lý collection
    # test_collection_management(db)
    
    # print_separator("KẾT THÚC TEST")
    # print("✅ Hoàn thành tất cả tests!")
    
    test_qa_system(db)

if __name__ == "__main__":
    main()
